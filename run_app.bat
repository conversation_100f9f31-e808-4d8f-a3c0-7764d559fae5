@echo off
REM Batch file to run Ollama chat app with one click

echo Checking for Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js is not installed. Please install from https://nodejs.org
    pause
    exit /b
)

echo Starting Ollama service...
start "" "ollama" serve

echo Starting web server...
start "" "cmd" /c "npx serve && pause"

timeout /t 2 >nul

echo Opening browser...
start "" "chrome" "http://localhost:3000"

echo App should now be running in your browser
pause
